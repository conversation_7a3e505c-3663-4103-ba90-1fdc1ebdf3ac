{"version": 3, "sources": ["../keystone.ts", "../schema.ts", "../auth.ts"], "sourcesContent": ["import { config } from '@keystone-6/core';\nimport { lists } from './schema';\nimport { withAuth, session } from './auth';\n\nexport default withAuth(\n  config({\n    db: {\n      provider: 'sqlite',\n      url: process.env.DATABASE_URL || 'file:./keystone.db',\n    },\n    lists,\n    session,\n    ui: {\n      isAccessAllowed: (context) => !!context.session?.data,\n    },\n    server: {\n      cors: { origin: ['http://localhost:3000'], credentials: true },\n    },\n  })\n);\n", "import { list } from '@keystone-6/core';\nimport { allowAll } from '@keystone-6/core/access';\nimport {\n  text,\n  relationship,\n  password,\n  timestamp,\n  select,\n} from '@keystone-6/core/fields';\nimport { document } from '@keystone-6/fields-document';\n\nexport const lists = {\n  User: list({\n    access: allowAll,\n    fields: {\n      name: text({ validation: { isRequired: true } }),\n      email: text({\n        validation: { isRequired: true },\n        isIndexed: 'unique',\n      }),\n      password: password({ validation: { isRequired: true } }),\n      posts: relationship({ ref: 'Post.author', many: true }),\n      createdAt: timestamp({\n        defaultValue: { kind: 'now' },\n      }),\n    },\n  }),\n\n  Post: list({\n    access: allowAll,\n    fields: {\n      title: text({ validation: { isRequired: true } }),\n      slug: text({\n        validation: { isRequired: true },\n        isIndexed: 'unique',\n        hooks: {\n          resolveInput: ({ operation, resolvedData, inputData }) => {\n            if (operation === 'create' && !inputData.slug && inputData.title) {\n              return inputData.title\n                .toLowerCase()\n                .replace(/[^a-z0-9]+/g, '-')\n                .replace(/(^-|-$)+/g, '');\n            }\n            return resolvedData.slug;\n          },\n        },\n      }),\n      status: select({\n        options: [\n          { label: 'Published', value: 'published' },\n          { label: 'Draft', value: 'draft' },\n        ],\n        defaultValue: 'draft',\n        ui: {\n          displayMode: 'segmented-control',\n        },\n      }),\n      content: document({\n        formatting: true,\n        layouts: [\n          [1, 1],\n          [1, 1, 1],\n          [2, 1],\n        ],\n        links: true,\n        dividers: true,\n      }),\n      publishedAt: timestamp(),\n      author: relationship({\n        ref: 'User.posts',\n        ui: {\n          displayMode: 'cards',\n          cardFields: ['name', 'email'],\n          inlineEdit: { fields: ['name', 'email'] },\n          linkToItem: true,\n          inlineConnect: true,\n        },\n      }),\n      createdAt: timestamp({\n        defaultValue: { kind: 'now' },\n      }),\n      updatedAt: timestamp({\n        defaultValue: { kind: 'now' },\n        db: { updatedAt: true },\n      }),\n    },\n    hooks: {\n      resolveInput: ({ operation, resolvedData, inputData }) => {\n        if (operation === 'update' && inputData.status === 'published' && !resolvedData.publishedAt) {\n          resolvedData.publishedAt = new Date();\n        }\n        return resolvedData;\n      },\n    },\n  }),\n};\n", "import { randomBytes } from 'crypto';\nimport { createAuth } from '@keystone-6/auth';\nimport { statelessSessions } from '@keystone-6/core/session';\n\nlet sessionSecret = process.env.SESSION_SECRET;\nif (!sessionSecret && process.env.NODE_ENV !== 'production') {\n  sessionSecret = randomBytes(32).toString('hex');\n}\n\nconst { withAuth } = createAuth({\n  listKey: 'User',\n  identityField: 'email',\n  sessionData: 'name createdAt',\n  secretField: 'password',\n  initFirstItem: {\n    fields: ['name', 'email', 'password'],\n  },\n});\n\nconst sessionMaxAge = 60 * 60 * 24 * 30; // 30 days\n\nconst session = statelessSessions({\n  maxAge: sessionMaxAge,\n  secret: sessionSecret!,\n});\n\nexport { withAuth, session };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,eAAuB;;;ACAvB,kBAAqB;AACrB,oBAAyB;AACzB,oBAMO;AACP,6BAAyB;AAElB,IAAM,QAAQ;AAAA,EACnB,UAAM,kBAAK;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,UAAM,oBAAK,EAAE,YAAY,EAAE,YAAY,KAAK,EAAE,CAAC;AAAA,MAC/C,WAAO,oBAAK;AAAA,QACV,YAAY,EAAE,YAAY,KAAK;AAAA,QAC/B,WAAW;AAAA,MACb,CAAC;AAAA,MACD,cAAU,wBAAS,EAAE,YAAY,EAAE,YAAY,KAAK,EAAE,CAAC;AAAA,MACvD,WAAO,4BAAa,EAAE,KAAK,eAAe,MAAM,KAAK,CAAC;AAAA,MACtD,eAAW,yBAAU;AAAA,QACnB,cAAc,EAAE,MAAM,MAAM;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAAA,EAED,UAAM,kBAAK;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN,WAAO,oBAAK,EAAE,YAAY,EAAE,YAAY,KAAK,EAAE,CAAC;AAAA,MAChD,UAAM,oBAAK;AAAA,QACT,YAAY,EAAE,YAAY,KAAK;AAAA,QAC/B,WAAW;AAAA,QACX,OAAO;AAAA,UACL,cAAc,CAAC,EAAE,WAAW,cAAc,UAAU,MAAM;AACxD,gBAAI,cAAc,YAAY,CAAC,UAAU,QAAQ,UAAU,OAAO;AAChE,qBAAO,UAAU,MACd,YAAY,EACZ,QAAQ,eAAe,GAAG,EAC1B,QAAQ,aAAa,EAAE;AAAA,YAC5B;AACA,mBAAO,aAAa;AAAA,UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,YAAQ,sBAAO;AAAA,QACb,SAAS;AAAA,UACP,EAAE,OAAO,aAAa,OAAO,YAAY;AAAA,UACzC,EAAE,OAAO,SAAS,OAAO,QAAQ;AAAA,QACnC;AAAA,QACA,cAAc;AAAA,QACd,IAAI;AAAA,UACF,aAAa;AAAA,QACf;AAAA,MACF,CAAC;AAAA,MACD,aAAS,iCAAS;AAAA,QAChB,YAAY;AAAA,QACZ,SAAS;AAAA,UACP,CAAC,GAAG,CAAC;AAAA,UACL,CAAC,GAAG,GAAG,CAAC;AAAA,UACR,CAAC,GAAG,CAAC;AAAA,QACP;AAAA,QACA,OAAO;AAAA,QACP,UAAU;AAAA,MACZ,CAAC;AAAA,MACD,iBAAa,yBAAU;AAAA,MACvB,YAAQ,4BAAa;AAAA,QACnB,KAAK;AAAA,QACL,IAAI;AAAA,UACF,aAAa;AAAA,UACb,YAAY,CAAC,QAAQ,OAAO;AAAA,UAC5B,YAAY,EAAE,QAAQ,CAAC,QAAQ,OAAO,EAAE;AAAA,UACxC,YAAY;AAAA,UACZ,eAAe;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,MACD,eAAW,yBAAU;AAAA,QACnB,cAAc,EAAE,MAAM,MAAM;AAAA,MAC9B,CAAC;AAAA,MACD,eAAW,yBAAU;AAAA,QACnB,cAAc,EAAE,MAAM,MAAM;AAAA,QAC5B,IAAI,EAAE,WAAW,KAAK;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,OAAO;AAAA,MACL,cAAc,CAAC,EAAE,WAAW,cAAc,UAAU,MAAM;AACxD,YAAI,cAAc,YAAY,UAAU,WAAW,eAAe,CAAC,aAAa,aAAa;AAC3F,uBAAa,cAAc,oBAAI,KAAK;AAAA,QACtC;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AC/FA,oBAA4B;AAC5B,kBAA2B;AAC3B,qBAAkC;AAElC,IAAI,gBAAgB,QAAQ,IAAI;AAChC,IAAI,CAAC,iBAAiB,QAAQ,IAAI,aAAa,cAAc;AAC3D,sBAAgB,2BAAY,EAAE,EAAE,SAAS,KAAK;AAChD;AAEA,IAAM,EAAE,SAAS,QAAI,wBAAW;AAAA,EAC9B,SAAS;AAAA,EACT,eAAe;AAAA,EACf,aAAa;AAAA,EACb,aAAa;AAAA,EACb,eAAe;AAAA,IACb,QAAQ,CAAC,QAAQ,SAAS,UAAU;AAAA,EACtC;AACF,CAAC;AAED,IAAM,gBAAgB,KAAK,KAAK,KAAK;AAErC,IAAM,cAAU,kCAAkB;AAAA,EAChC,QAAQ;AAAA,EACR,QAAQ;AACV,CAAC;;;AFpBD,IAAO,mBAAQ;AAAA,MACb,qBAAO;AAAA,IACL,IAAI;AAAA,MACF,UAAU;AAAA,MACV,KAAK,QAAQ,IAAI,gBAAgB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI;AAAA,MACF,iBAAiB,CAAC,YAAY,CAAC,CAAC,QAAQ,SAAS;AAAA,IACnD;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,EAAE,QAAQ,CAAC,uBAAuB,GAAG,aAAa,KAAK;AAAA,IAC/D;AAAA,EACF,CAAC;AACH;", "names": ["import_core"]}