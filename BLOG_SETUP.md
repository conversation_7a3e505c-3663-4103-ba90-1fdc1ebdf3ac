# Blog System Setup with KeystoneJS 6

This project now includes a complete blog system powered by KeystoneJS 6 for content management and Next.js for the frontend.

## Features

- **Content Management**: Full-featured admin interface powered by KeystoneJS 6
- **Rich Text Editor**: Document field with formatting, layouts, links, and dividers
- **Preview Mode**: Preview draft content with a secret token system
- **SEO Friendly**: Automatic slug generation and metadata
- **Responsive Design**: Mobile-friendly blog pages with Tailwind CSS

## Getting Started

### 1. Environment Setup

Copy the example environment file and configure your settings:

```bash
cp .env.example .env.local
```

Update the following variables in `.env.local`:
- `SESSION_SECRET`: Generate a secure random string for production
- `PREVIEW_SECRET`: Generate a secure random string for preview mode
- `DATABASE_URL`: SQLite database path (default: `file:./keystone.db`)

### 2. Install Dependencies

Dependencies are already installed, but if you need to reinstall:

```bash
npm install --legacy-peer-deps
```

### 3. Run the Development Servers

Start both KeystoneJS and Next.js in development mode:

```bash
npm run dev:all
```

Or run them separately:

```bash
# Terminal 1: Start KeystoneJS admin
npm run keystone

# Terminal 2: Start Next.js frontend
npm run dev
```

### 4. Access the Applications

- **Next.js Frontend**: http://localhost:3000
- **KeystoneJS Admin**: http://localhost:3001
- **Blog Pages**: 
  - Blog listing: http://localhost:3000/blog
  - Individual posts: http://localhost:3000/blog/[slug]

## Content Management

### Creating Your First User

1. Visit http://localhost:3001
2. Create your first admin user account
3. Log in to access the admin interface

### Creating Blog Posts

1. Go to the "Posts" section in the admin
2. Click "Create Post"
3. Fill in the required fields:
   - **Title**: The post title (slug auto-generated)
   - **Content**: Rich text content using the document editor
   - **Status**: Choose "Draft" or "Published"
   - **Author**: Select the author (yourself)

### Publishing Posts

- Set status to "Published" to make posts visible on the frontend
- The `publishedAt` timestamp is automatically set when publishing
- Draft posts are only visible in preview mode

## Preview Mode

Preview mode allows you to view draft content before publishing.

### Enabling Preview Mode

Visit the preview URL with your secret token:
```
http://localhost:3000/api/preview?secret=your-preview-secret&slug=your-post-slug
```

This will:
1. Validate the secret token
2. Set a preview cookie
3. Redirect to the blog post with preview enabled

### Disabling Preview Mode

Send a POST request to the preview endpoint:
```bash
curl -X POST http://localhost:3000/api/preview
```

Or click "Exit Preview" in the preview banner.

## Blog Structure

### Post Model Fields

- `title`: Text field (required)
- `slug`: Unique text field (auto-generated from title)
- `status`: Select field ("draft" or "published")
- `content`: Rich document field with formatting options
- `publishedAt`: Timestamp (auto-set when publishing)
- `author`: Relationship to User model
- `createdAt`: Auto-generated creation timestamp
- `updatedAt`: Auto-updated modification timestamp

### Frontend Pages

- `/blog`: Blog listing page showing published posts
- `/blog/[slug]`: Individual blog post page
- Preview mode shows draft content when enabled

## Customization

### Styling

The blog pages use Tailwind CSS classes. Customize the styling by editing:
- `app/blog/page.tsx` (blog listing)
- `app/blog/[slug]/page.tsx` (individual posts)
- `components/PreviewBanner.tsx` (preview mode banner)

### Document Renderer

Customize how rich text content is rendered by modifying the `renderers` prop in the `DocumentRenderer` component in `app/blog/[slug]/page.tsx`.

### GraphQL Queries

Modify the GraphQL queries in `lib/keystone.ts` to fetch additional fields or implement pagination.

## Production Deployment

### Environment Variables

Set the following environment variables in production:
- `SESSION_SECRET`: Secure random string (32+ characters)
- `PREVIEW_SECRET`: Secure random string for preview mode
- `DATABASE_URL`: Production database connection string
- `KEYSTONE_URL`: Production KeystoneJS URL

### Build Commands

```bash
# Build KeystoneJS
npm run keystone:build

# Build Next.js
npm run build

# Start production servers
npm run keystone:start  # KeystoneJS
npm start              # Next.js
```

## Troubleshooting

### React Version Conflicts

If you encounter React version conflicts, install dependencies with:
```bash
npm install --legacy-peer-deps
```

### Database Issues

If you encounter database issues, delete the SQLite database file and restart:
```bash
rm keystone.db*
npm run keystone
```

### Preview Mode Not Working

1. Check that `PREVIEW_SECRET` is set correctly
2. Ensure the preview cookie is being set
3. Verify the GraphQL endpoint is accessible

## Next Steps

- Add image upload capabilities with Cloudinary
- Implement categories and tags
- Add search functionality
- Set up automated deployments
- Configure production database (PostgreSQL/MySQL)
