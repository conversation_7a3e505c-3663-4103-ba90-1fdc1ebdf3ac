import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const secret = searchParams.get('secret');
  const slug = searchParams.get('slug');

  // Check the secret and next parameters
  // This secret should only be known to this API route and the CMS
  if (secret !== process.env.PREVIEW_SECRET || !slug) {
    return new NextResponse('Invalid token', { status: 401 });
  }

  // Fetch the headless CMS to check if the provided `slug` exists
  // getPostBySlug would implement the actual fetching logic:
  // const post = await getPostBySlug(slug)
  // if (!post) {
  //   return new NextResponse('Invalid slug', { status: 401 })
  // }

  // Enable Preview Mode by setting the cookies
  const response = NextResponse.redirect(new URL(`/blog/${slug}?preview=true`, request.url));
  
  // Set preview cookie
  response.cookies.set('__preview', 'true', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60, // 1 hour
  });

  return response;
}

export async function POST(request: NextRequest) {
  // Clear the preview mode cookies
  const response = NextResponse.json({ message: 'Preview mode disabled' });
  
  response.cookies.set('__preview', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
  });

  return response;
}
