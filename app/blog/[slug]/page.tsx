import { notFound } from 'next/navigation';
import { DocumentRenderer } from '@keystone-6/document-renderer';
import { getPostBySlug, isPreviewMode } from '@/lib/keystone';
import { PreviewBanner } from '@/components/PreviewBanner';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    preview?: string;
  };
}

export default async function BlogPostPage({ params, searchParams }: BlogPostPageProps) {
  const preview = isPreviewMode() || searchParams.preview === 'true';
  const post = await getPostBySlug(params.slug, preview);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {preview && <PreviewBanner />}
      
      <article className="max-w-4xl mx-auto px-4 py-8">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {post.title}
          </h1>
          
          <div className="flex items-center text-gray-600 text-sm space-x-4">
            {post.author && (
              <span>By {post.author.name}</span>
            )}
            
            {post.publishedAt && (
              <time dateTime={post.publishedAt}>
                {new Date(post.publishedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </time>
            )}
            
            {post.status === 'draft' && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Draft
              </span>
            )}
          </div>
        </header>

        <div className="prose prose-lg max-w-none">
          {post.content?.document && (
            <DocumentRenderer
              document={post.content.document}
              renderers={{
                // Custom renderers can be added here
                block: {
                  paragraph: ({ children }) => (
                    <p className="mb-4 text-gray-700 leading-relaxed">{children}</p>
                  ),
                  heading: ({ level, children }) => {
                    const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
                    const headingClasses = {
                      1: 'text-3xl font-bold text-gray-900 mt-8 mb-4',
                      2: 'text-2xl font-semibold text-gray-900 mt-6 mb-3',
                      3: 'text-xl font-semibold text-gray-900 mt-5 mb-2',
                      4: 'text-lg font-semibold text-gray-900 mt-4 mb-2',
                      5: 'text-base font-semibold text-gray-900 mt-3 mb-2',
                      6: 'text-sm font-semibold text-gray-900 mt-2 mb-1',
                    };
                    
                    return (
                      <HeadingTag className={headingClasses[level as keyof typeof headingClasses]}>
                        {children}
                      </HeadingTag>
                    );
                  },
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 my-4">
                      {children}
                    </blockquote>
                  ),
                  code: ({ children }) => (
                    <pre className="bg-gray-100 rounded-lg p-4 overflow-x-auto my-4">
                      <code className="text-sm">{children}</code>
                    </pre>
                  ),
                },
                inline: {
                  bold: ({ children }) => (
                    <strong className="font-semibold">{children}</strong>
                  ),
                  italic: ({ children }) => (
                    <em className="italic">{children}</em>
                  ),
                  code: ({ children }) => (
                    <code className="bg-gray-100 px-1 py-0.5 rounded text-sm">
                      {children}
                    </code>
                  ),
                  link: ({ children, href }) => (
                    <a
                      href={href}
                      className="text-blue-600 hover:text-blue-800 underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {children}
                    </a>
                  ),
                },
              }}
            />
          )}
        </div>
      </article>
    </div>
  );
}

export async function generateMetadata({ params }: BlogPostPageProps) {
  const post = await getPostBySlug(params.slug);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  return {
    title: post.title,
    description: `Blog post: ${post.title}`,
  };
}
