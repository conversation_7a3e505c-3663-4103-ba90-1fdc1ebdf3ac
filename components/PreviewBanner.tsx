'use client';

import { useState } from 'react';

export function PreviewBanner() {
  const [isVisible, setIsVisible] = useState(true);

  const exitPreview = async () => {
    try {
      await fetch('/api/preview', { method: 'POST' });
      window.location.reload();
    } catch (error) {
      console.error('Failed to exit preview mode:', error);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="bg-yellow-400 border-b border-yellow-500">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-12">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-yellow-800"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-yellow-800">
                Preview Mode: You are viewing draft content that is not publicly visible.
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={exitPreview}
              className="bg-yellow-800 hover:bg-yellow-900 text-white text-sm font-medium px-3 py-1 rounded transition-colors"
            >
              Exit Preview
            </button>
            <button
              onClick={() => setIsVisible(false)}
              className="text-yellow-800 hover:text-yellow-900 transition-colors"
              aria-label="Dismiss banner"
            >
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
