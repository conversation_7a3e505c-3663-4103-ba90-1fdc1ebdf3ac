"use client";

import { useEffect, useState } from "react";
import { ToggleGroup, ToggleGroupItem } from "../ui/toggle-group";

export default function LocaleSwitcher() {
  const [currentLocale, setCurrentLocale] = useState("ar");

  useEffect(() => {
    const cookieLocale = document.cookie.match(/(?:^|; )locale=([^;]*)/)?.[1];
    setCurrentLocale(cookieLocale || localStorage.getItem("locale") || "ar");
  }, []);
  const switchLocale = (locale: string) => {
    document.cookie = `locale=${locale}; path=/`;
    location.reload();
  };

  return (
    <ToggleGroup
      type="single"
      value={currentLocale}
      onValueChange={(value) => value && switchLocale(value as "ar" | "en")}
      size="sm"
      className={`bg-black/20 backdrop-blur-sm !rounded-full p-1 border border-white/10 `}
    >
      <ToggleGroupItem
        value="ar"
        className="!rounded-full px-3 ml-1 text-sm font-medium transition-all data-[state=on]:bg-white data-[state=on]:text-gray-900 data-[state=off]:text-white/70 data-[state=off]:hover:text-white hover:bg-white/10"
      >
        العربية
      </ToggleGroupItem>
      <ToggleGroupItem
        value="en"
        className="!rounded-full px-3 mr-1 text-sm font-medium transition-all data-[state=on]:bg-white data-[state=on]:text-gray-900 data-[state=off]:text-white/70 data-[state=off]:hover:text-white hover:bg-white/10"
      >
        English
      </ToggleGroupItem>
    </ToggleGroup>
  );
}
