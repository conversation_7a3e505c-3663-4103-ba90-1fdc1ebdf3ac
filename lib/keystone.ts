import { cookies } from 'next/headers';

const KEYSTONE_URL = process.env.KEYSTONE_URL || 'http://localhost:3001';

export function isPreviewMode(): boolean {
  const cookieStore = cookies();
  return cookieStore.get('__preview')?.value === 'true';
}

export async function getPostBySlug(slug: string, preview = false) {
  const query = `
    query GetPost($slug: String!) {
      post(where: { slug: $slug }) {
        id
        title
        slug
        status
        content {
          document
        }
        publishedAt
        createdAt
        updatedAt
        author {
          name
        }
      }
    }
  `;

  try {
    const response = await fetch(`${KEYSTONE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables: { slug },
      }),
      cache: preview ? 'no-store' : 'default',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch post');
    }

    const { data } = await response.json();
    const post = data?.post;

    // If not in preview mode, only return published posts
    if (!preview && post?.status !== 'published') {
      return null;
    }

    return post;
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

export async function getAllPosts(preview = false) {
  const query = `
    query GetPosts {
      posts(
        orderBy: { publishedAt: desc }
        ${!preview ? 'where: { status: { equals: published } }' : ''}
      ) {
        id
        title
        slug
        status
        publishedAt
        createdAt
        author {
          name
        }
      }
    }
  `;

  try {
    const response = await fetch(`${KEYSTONE_URL}/api/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
      }),
      cache: preview ? 'no-store' : 'default',
    });

    if (!response.ok) {
      throw new Error('Failed to fetch posts');
    }

    const { data } = await response.json();
    return data?.posts || [];
  } catch (error) {
    console.error('Error fetching posts:', error);
    return [];
  }
}
